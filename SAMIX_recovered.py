import os
import time
import random
import datetime
from datetime import timedelta
import hashlib
import uuid
import sys
import pytz
import requests
import colorama
from colorama import Fore, Style
import csv
import tkinter as tk

# Color constants
RESET = '\x1b[0m'
BOLD = '\x1b[1m'
BRIGHT_RED = '\x1b[91m'
BRIGHT_GREEN = '\x1b[92m'
BRIGHT_YELLOW = '\x1b[93m'
BRIGHT_BLUE = '\x1b[94m'
BRIGHT_MAGENTA = '\x1b[95m'
BRIGHT_CYAN = '\x1b[96m'
WHITE = '\x1b[97m'
UNDERLINE = '\x1b[4m'
PURPLE = '\x1b[0;35m'

def clear_screen():
    """Clear the terminal screen"""
    if os.name != 'posix':
        os.system('cls')
    else:
        os.system('clear')

def display_banner():
    """Display the application banner"""
    banner = f"""


{BRIGHT_GREEN}
             ████████████████████████████████████████████████████████████████████████████████████████████████████████████████
{BRIGHT_BLUE}
                                     ██╗░░░██╗░ █████╗ ██╗░░░░░ ██╗░░██╗███████╗  ██╗░░██╗░█████╗░░█████╗░██╗░░██╗
                                     ██║░░░██║ ██╔══██╗██║░░░░░ ██║░░██║██╔════╝  ██║░░██║██╔══██╗██╔══██╗██║░██╔╝
                                     ██║░█╗██║ ██║░░██║██║░░░░░╚██╗░██╔╝█████╗░░  ███████║███████║██║░░╚═╝█████═╝░
                                     ██║███╗██║██║░░██║██║░░░░░░╚████╔╝░██╔══╝░░  ██╔══██║██╔══██║██║░░██╗██╔═██╗░
                                     ╚███╔███╔╝╚█████╔╝███████╗░░╚██╔╝░░███████╗  ██║░░██║██║░░██║╚█████╔╝██║░╚██╗
                                     ░╚══╝╚══╝░░╚════╝░╚══════╝░░░╚═╝░░░╚══════╝  ╚═╝░░╚═╝╚═╝░░╚═╝░╚════╝░╚═╝░░╚═╝


                                          ═══════════════════════════════════════════
                                          ╔═════════════════════════════════════════╗
                                          ║[+] DEVOLPER   : WOLVE TRADING           ║
                                          ║[+] TELEGRAM   : @Wolvestrading1         ║
                                          ║[+] VERSION    : 1.9                     ║
                                          ║[+] TOOLS      : SIGNAL GENERATOR        ║
                                          ║[+] {license_color}{license_status:<37}{RESET} ║
                                          ╚═════════════════════════════════════════╝
                                          ═══════════════════════════════════════════
{BRIGHT_GREEN}
             ████████████████████████████████████████████████████████████████████████████████████████████████████████████████


"""
    print(f"{BOLD}{BRIGHT_CYAN}{banner}{RESET}")

def developer_info():
    """Display developer information"""
    # Developer info is already included in the banner
    pass

def check_license():
    """Check if the license is still valid"""
    EXPIRY_DATE = datetime.datetime(2029, 3, 10)  # March 10, 2029
    current_date = datetime.datetime.now()
    
    if current_date > EXPIRY_DATE:
        print(f"{BRIGHT_RED}❌ License expired! Please update your software.{RESET}")
        sys.exit()
    else:
        print(f"{BRIGHT_GREEN}✅ License is active! Starting the program...{RESET}")
        print(f"{BRIGHT_GREEN}🔥 Software is running!{RESET}")

def authenticate():
    """Handle user authentication"""
    User_names_database = "KOCHI"
    password_database = "123"

    entered_user_name = input(f"{BRIGHT_YELLOW}𓆩⚝𓆪ENTER YOUR SOKET ID𓆩⚝𓆪:{BRIGHT_YELLOW}")

    if entered_user_name.strip().upper() != User_names_database and entered_user_name.strip().upper() != " KOCHI":
        print(f"{BRIGHT_RED}❌ LOGIN  FAILED CONTACT @Mustaqsami6✅{RESET}")
        return False

    entered_password = input(f"{BRIGHT_BLUE}𓆩⚝𓆪ENTER YOUR PASSWORD𓆩⚝𓆪:{BRIGHT_BLUE}")

    if entered_password.strip() == password_database:
        print(f"{BRIGHT_GREEN}✅ AUTHENTICATION SUCCESSFUL! WELCOME, SAMI!{RESET}")
        return True
    else:
        # Second chance for password
        entered_password = input(f"{BRIGHT_BLUE}⚝𓆪ENTER YOUR PASSWORD𓆩⚝𓆪:{BRIGHT_BLUE}")
        if entered_password.strip() == password_database:
            print(f"{BRIGHT_GREEN}✅ AUTHENTICATION SUCCESSFUL! WELCOME, SAMI!{RESET}")
            return True
        else:
            print(f"{BRIGHT_RED}❌ LOGIN  FAILED CONTACT @Mustaqsami6✅{RESET}")
            return False

def select_mode():
    """Select trading mode"""
    print(f"{BRIGHT_CYAN}Available Modes:")
    print("1: Blackout")
    print("2: Normal{RESET}")
    
    while True:
        mode = input(f"{BRIGHT_YELLOW}Enter the number corresponding to the mode: {RESET}")
        if mode in ('1', '2'):
            return mode
        print(f"{BRIGHT_RED}Invalid mode! Please select 1 or 2.{RESET}")

def select_broker():
    """Select trading broker"""
    print(f"{BRIGHT_CYAN}[*] Available Brokers: QUOTEX, EXNOVA, AVALON{RESET}")
    
    while True:
        selected_broker = input(f"{BRIGHT_YELLOW}𓆩⚝𓆪 SELECT BROKER (default - quotex): {RESET}").strip().lower()
        if not selected_broker:
            selected_broker = "quotex"
        
        if selected_broker in ('quotex', 'avalon', 'exnova'):
            return selected_broker.upper()
        print(f"{BRIGHT_RED}Invalid broker. Please choose quotex, avalon, exnova.{RESET}")

def get_accuracy_value():
    """Get accuracy setting"""
    while True:
        try:
            Accuracy_value = input(f"{BRIGHT_YELLOW}𓆩✧𓆪 CHOSE ACCURACY  (ex. 1.(ADVANCE), 2.(NORMAL), 3.(FIGHTER),: {RESET}")
            if Accuracy_value.isdigit() and int(Accuracy_value) in (1, 2, 3):
                return int(Accuracy_value)
            print(f"{BRIGHT_RED}Oops, out of range. Please enter a value 1, 2 or 3.{RESET}")
        except ValueError:
            print(f"{BRIGHT_RED}Invalid input. Please enter a number.{RESET}")

def get_martingale_value():
    """Get martingale setting"""
    while True:
        try:
            filter_value = input(f"{BRIGHT_YELLOW}𓆩✧𓆪 HOW MANY MARTINGALE DO YOU NEED? (ex. 0->2): {RESET}")
            if filter_value.isdigit() and 0 <= int(filter_value) <= 2:
                return int(filter_value)
            print(f"{BRIGHT_RED}Oops, out of range. Please enter a value 0-2.{RESET}")
        except ValueError:
            print(f"{BRIGHT_RED}Invalid input. Please enter a number.{RESET}")

def get_fighter_strategy():
    """Get fighter strategy setting"""
    while True:
        try:
            separate = input(f"{BRIGHT_YELLOW}𓆩✧𓆪 FIGHTER STARTEGY (ex. 1-No, 2-Yes): {RESET}")
            if separate.isdigit() and int(separate) in (1, 2):
                return int(separate)
            print(f"{BRIGHT_RED}Oops, out of range. Please enter a value 1 or 2.{RESET}")
        except ValueError:
            print(f"{BRIGHT_RED}Invalid input. Please enter a number.{RESET}")

def get_stc_value():
    """Get STC (Trend Cycle) value"""
    while True:
        try:
            stc_value = input(f"{BRIGHT_YELLOW}𓆩✧𓆪  TREND CYCLE (STC) - (ex. 20): {RESET}")
            if stc_value.isdigit() and int(stc_value) == 20:
                return int(stc_value)
            print(f"{BRIGHT_RED}Oops, out of range. Please enter a value 20.{RESET}")
        except ValueError:
            print(f"{BRIGHT_RED}Invalid input. Please enter 20.{RESET}")

def get_rma_value():
    """Get RMA (Pragton Strategy) value"""
    while True:
        try:
            rma_value = input(f"{BRIGHT_YELLOW}𓆩✧𓆪 PRAGTON STATERGY  (ex. 1.(9 period), 2.(11 period): {RESET}")
            if rma_value.isdigit() and int(rma_value) in (1, 2):
                return int(rma_value)
            print(f"{BRIGHT_RED}Oops, out of range. Please enter a value 1 or 2.{RESET}")
        except ValueError:
            print(f"{BRIGHT_RED}Invalid input. Please enter a number.{RESET}")

def get_backtest_settings():
    """Get backtest configuration"""
    while True:
        backtest = input(f"{BRIGHT_YELLOW}𓆩✧𓆪 Advance Backtest (1.ON 0.OFF): {RESET}")
        if backtest in ('0', '1'):
            break
        print(f"{BRIGHT_RED}Invalid input. Please enter 0 or 1.{RESET}")
    
    if backtest == '1':
        # Get percentage settings
        while True:
            try:
                percentage = float(input(f"{BRIGHT_YELLOW}𓆩✧𓆪 Enter the minimum percentage (50-90): {RESET}"))
                if 50 <= percentage <= 90:
                    break
                print(f"{BRIGHT_RED}Please enter a value between 50 and 90.{RESET}")
            except ValueError:
                print(f"{BRIGHT_RED}Invalid input. Please enter a number.{RESET}")
        
        while True:
            try:
                max_percentage = float(input(f"{BRIGHT_YELLOW}𓆩✧𓆪 Enter the maximum percentage (78-95): {RESET}"))
                if 78 <= max_percentage <= 95:
                    break
                print(f"{BRIGHT_RED}Please enter a value between 78 and 95.{RESET}")
            except ValueError:
                print(f"{BRIGHT_RED}Invalid input. Please enter a number.{RESET}")
        
        while True:
            try:
                days = int(input(f"{BRIGHT_YELLOW}𓆩✧𓆪 Enter the number of Backtest days to analyze (eg. 20): {RESET}"))
                if 0 <= days <= 30:
                    break
                print(f"{BRIGHT_RED}Invalid input. Please enter a number between 0 and 30.{RESET}")
            except ValueError:
                print(f"{BRIGHT_RED}Invalid input. Please enter a number.{RESET}")
        
        return True, percentage, max_percentage, days
    
    return False, None, None, None

def validate_time_format(time_str):
    """Validate time format HH:MM"""
    try:
        datetime.datetime.strptime(time_str, '%H:%M')
        return True
    except ValueError:
        return False

def get_time_settings():
    """Get start and end time settings"""
    while True:
        start_time_input = input(f"{BRIGHT_YELLOW}𓆩✧𓆪[+] Start Time (HH:MM): {RESET}")
        if validate_time_format(start_time_input):
            break
        print(f"{BRIGHT_RED}Invalid time format! Please use HH:MM format.{RESET}")
    
    while True:
        end_time_input = input(f"{BRIGHT_YELLOW}𓆩✧𓆪[+] End Time (HH:MM): {RESET}")
        if validate_time_format(end_time_input):
            break
        print(f"{BRIGHT_RED}Invalid time format! Please use HH:MM format.{RESET}")
    
    start_time = datetime.datetime.strptime(start_time_input, '%H:%M').time()
    end_time = datetime.datetime.strptime(end_time_input, '%H:%M').time()
    
    if start_time >= end_time:
        print(f"{BRIGHT_RED}Start Time must be earlier than End Time! Exiting...{RESET}")
        sys.exit()
    
    return start_time, end_time

def select_assets():
    """Select trading assets/pairs"""
    assets = [
        'AUD/CAD-OTC', 'AUD/CHF-OTC', 'AUD/JPY-OTC', 'AUD/NZD-OTC', 'AUD/USD-OTC',
        'BRL/USD-OTC', 'BTC/USD-OTC', 'CAD/JPY-OTC', 'CAD/CHF-OTC', 'CHF/JPY-OTC',
        'DJIUSD', 'EUR/JPY-OTC', 'EUR/SGD-OTC', 'EUR/USD-OTC', 'F40EUR',
        'FB-OTC', 'FTSGBP', 'GBP/JPY-OTC', 'GBP/USD-OTC', 'INTC-OTC'
    ]
    
    print(f"{BRIGHT_CYAN}═══════════════════════════════════════════")
    print("Available Assets:")
    print("═══════════════════════════════════════════{RESET}")
    
    for i, asset in enumerate(assets, start=1):
        print(f"{i:2}: {asset:<15}\t", end="")
        if i % 4 == 0:
            print()
    print(f"\n{BRIGHT_CYAN}═══════════════════════════════════════════{RESET}")
    
    while True:
        pair_choice = input(f"{BRIGHT_YELLOW}Enter the numbers corresponding to the pairs (e.g., 1,2,3): {RESET}")
        selected_pairs = []
        
        try:
            for num in pair_choice.split(','):
                num = num.strip()
                if num.isdigit() and 1 <= int(num) <= len(assets):
                    selected_pairs.append(assets[int(num) - 1])
            
            if selected_pairs:
                break
            print(f"{BRIGHT_RED}No valid pairs selected. Exiting...{RESET}")
        except:
            print(f"{BRIGHT_RED}Invalid input. Please enter numbers separated by commas.{RESET}")
    
    print(f"{BRIGHT_GREEN}Selected Pairs: {', '.join(selected_pairs)}{RESET}")
    return selected_pairs

def generate_signals(selected_pairs, start_time, end_time):
    """Generate trading signals"""
    print(f"{BRIGHT_CYAN}==================================================")
    print("UTC +6:00 TIMEFRAME:")
    print(f"=================================================={RESET}")

    signals = 0

    while True:
        current_time = datetime.datetime.now()

        # Check if current time is within trading hours
        if start_time <= current_time.time() <= end_time:
            for pair in selected_pairs:
                signal_time = current_time.strftime('%H:%M:%S')
                direction = random.choice(['CALL', 'PUT'])

                print(f"{BRIGHT_GREEN}{signal_time} > {pair} > {direction}{RESET}")
                signals += 1

                # Wait 5 minutes between signals
                time.sleep(5 * 60)  # 5 minutes = 300 seconds

                # Show generated signals count periodically
                if signals % 10 == 0:
                    print(f"{BRIGHT_YELLOW}Generated Signals: {signals}{RESET}")
                    print(f"{BRIGHT_MAGENTA}PRAGTON X2{RESET}")

                # Sleep for 10 minutes (600 seconds) after every batch
                if signals % 5 == 0:
                    time.sleep(600)
        else:
            print(f"{BRIGHT_YELLOW}Outside trading hours. Waiting...{RESET}")
            time.sleep(60)

def main():
    """Main application function"""
    clear_screen()
    display_banner()
    developer_info()
    
    # Check license
    check_license()
    
    # Authentication
    if not authenticate():
        sys.exit()
    
    # Configuration
    mode = select_mode()
    broker = select_broker()
    accuracy = get_accuracy_value()
    martingale = get_martingale_value()
    fighter = get_fighter_strategy()
    stc = get_stc_value()
    rma = get_rma_value()
    
    # Backtest settings
    backtest_enabled, min_perc, max_perc, days = get_backtest_settings()
    
    # Time settings
    start_time, end_time = get_time_settings()
    
    # Asset selection
    selected_pairs = select_assets()
    
    # Start generating signals
    print(f"{BRIGHT_GREEN}Starting signal generation...{RESET}")
    generate_signals(selected_pairs, start_time, end_time)

if __name__ == "__main__":
    main()
