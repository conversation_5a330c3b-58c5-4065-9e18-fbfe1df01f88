import os
import time
import random
import datetime
from datetime import timedelta
import hashlib
import uuid
import sys
import pytz
import requests
import colorama
from colorama import Fore, Style
import csv
import tkinter as tk

# Initialize colorama
colorama.init()

# Color constants
RESET = Style.RESET_ALL
BOLD = Style.BRIGHT
BRIGHT_RED = Fore.RED + Style.BRIGHT
BRIGHT_GREEN = Fore.GREEN + Style.BRIGHT
BRIGHT_YELLOW = Fore.YELLOW + Style.BRIGHT
BRIGHT_BLUE = Fore.BLUE + Style.BRIGHT
BRIGHT_MAGENTA = Fore.MAGENTA + Style.BRIGHT
BRIGHT_CYAN = Fore.CYAN + Style.BRIGHT
WHITE = Fore.WHITE
UNDERLINE = '\033[4m'
PURPLE = Fore.MAGENTA

def clear_screen():
    if os.name != 'posix':
        os.system('cls')
    else:
        os.system('clear')

def display_banner():
    banner = f"""


{BRIGHT_GREEN}
             ████████████████████████████████████████████████████████████████████████████████████████████████████████████████
{BRIGHT_BLUE}
                                     ██╗░░░██╗░█████╗░██╗░░░░░██╗░░░██╗███████╗  ██╗░░██╗░█████╗░░█████╗░██╗░░██╗
                                     ██║░░░██║██╔══██╗██║░░░░░██║░░░██║██╔════╝  ██║░░██║██╔══██╗██╔══██╗██║░██╔╝
                                     ██║░█╗██║██║░░██║██║░░░░░╚██╗░██╔╝█████╗░░  ███████║███████║██║░░╚═╝█████═╝░
                                     ██║███╗██║██║░░██║██║░░░░░░╚████╔╝░██╔══╝░░  ██╔══██║██╔══██║██║░░██╗██╔═██╗░
                                     ╚███╔███╔╝╚█████╔╝███████╗░░╚██╔╝░░███████╗  ██║░░██║██║░░██║╚█████╔╝██║░╚██╗
                                     ░╚══╝╚══╝░░╚════╝░╚══════╝░░░╚═╝░░░╚══════╝  ╚═╝░░╚═╝╚═╝░░╚═╝░╚════╝░╚═╝░░╚═╝


                                          ═══════════════════════════════════════════
                                          ╔═════════════════════════════════════════╗
                                          ║[+] DEVOLPER   : WOLVE TRADING           ║
                                          ║[+] TELEGRAM   : @TradingBYMKT           ║
                                          ║[+] VERSION    : 1.9                     ║
                                          ║[+] TOOLS      : SIGNAL GENERATOR        ║
                                          ║[+] WOLVE X   : LICENSE ACTIVED ✓        ║
                                          ╚═════════════════════════════════════════╝
                                          ═══════════════════════════════════════════
{BRIGHT_GREEN}
             ████████████████████████████████████████████████████████████████████████████████████████████████████████████████


"""
    print(f"{BOLD}{BRIGHT_CYAN}{banner}{RESET}")

def developer_info():
    # Developer info is already included in the banner
    pass

# Check license expiry
EXPIRY_DATE = datetime.datetime(2029, 3, 10)
current_date = datetime.datetime.now()

if current_date > EXPIRY_DATE:
    print(f"{BRIGHT_RED}License expired! Contact developer.{RESET}")
    exit()

# Authentication
User_names_database = "WOLVE"
password_database = "123"

entered_user_name = input(f"{BRIGHT_YELLOW}𓆩⚝𓆪ENTER YOUR SOKET ID𓆩⚝𓆪:{BRIGHT_YELLOW}")

if entered_user_name.strip().upper() != User_names_database and entered_user_name.strip().upper() != " WOLVE":
    print(f"{BRIGHT_RED}❌ LOGIN  FAILED CONTACT @Mustaqsami6✅{RESET}")
    exit()

entered_password = input(f"{BRIGHT_BLUE}𓆩⚝𓆪ENTER YOUR PASSWORD𓆩⚝𓆪:{BRIGHT_BLUE}")

if entered_password.strip() == password_database:
    print(f"{BRIGHT_GREEN}✅ AUTHENTICATION SUCCESSFUL! WELCOME, SAMI!{RESET}")
else:
    # Second chance for password
    entered_password = input(f"{BRIGHT_BLUE}⚝𓆪ENTER YOUR PASSWORD𓆩⚝𓆪:{BRIGHT_BLUE}")
    if entered_password.strip() == password_database:
        print(f"{BRIGHT_GREEN}✅ AUTHENTICATION SUCCESSFUL! WELCOME, SAMI!{RESET}")
    else:
        print(f"{BRIGHT_RED}❌ LOGIN  FAILED CONTACT @Mustaqsami6✅{RESET}")
        exit()

clear_screen()
display_banner()
developer_info()

# Get trading mode
while True:
    mode = input(f"{BOLD}{BRIGHT_BLUE}𓆩✧𓆪 Select Mode (1.LIVE 2.DEMO): {RESET}{BRIGHT_MAGENTA}")
    if mode.strip().lower() in ['1', '2', 'live', 'demo']:
        break
    print(f"{BRIGHT_RED}Invalid selection. Please choose 1 or 2.{RESET}")

# Get broker selection
while True:
    selected_broker = input(f"{BOLD}{BRIGHT_RED}𓆩✧𓆪 Select Broker (1.QUOTEX 2.EXNOVA 3.AVALON): {RESET}{BRIGHT_MAGENTA}")
    if selected_broker.strip() in ['1', '2', '3']:
        BROKER = ['QUOTEX', 'EXNOVA', 'AVALON'][int(selected_broker) - 1]
        break
    print(f"{BRIGHT_RED}Invalid selection. Please choose 1, 2, or 3.{RESET}")

# Get accuracy setting
while True:
    Accuracy_value = input(f"{BOLD}{BRIGHT_YELLOW}𓆩✧𓆪 Accuracy (1.HIGH 2.MEDIUM 3.LOW): {RESET}{BRIGHT_MAGENTA}")
    if Accuracy_value.strip() in ['1', '2', '3']:
        break
    print(f"{BRIGHT_RED}Invalid selection. Please choose 1, 2, or 3.{RESET}")

# Get filter setting
while True:
    filter_value = input(f"{BOLD}{BRIGHT_GREEN}𓆩✧𓆪 Filter (1.ON 2.OFF): {RESET}{BRIGHT_MAGENTA}")
    if filter_value.strip() in ['1', '2']:
        break
    print(f"{BRIGHT_RED}Invalid selection. Please choose 1 or 2.{RESET}")

# Get separate setting
while True:
    separate = input(f"{BOLD}{BRIGHT_CYAN}𓆩✧𓆪 Separate (1.ON 2.OFF): {RESET}{BRIGHT_MAGENTA}")
    if separate.strip() in ['1', '2']:
        break
    print(f"{BRIGHT_RED}Invalid selection. Please choose 1 or 2.{RESET}")

# Get STC setting
while True:
    stc_value = input(f"{BOLD}{BRIGHT_MAGENTA}𓆩✧𓆪 STC (1.ON 2.OFF): {RESET}{BRIGHT_MAGENTA}")
    if stc_value.strip() in ['1', '2']:
        break
    print(f"{BRIGHT_RED}Invalid selection. Please choose 1 or 2.{RESET}")

# Get RMA setting
while True:
    rma_value = input(f"{BOLD}{BRIGHT_YELLOW}𓆩✧𓆪 RMA (1.ON 2.OFF): {RESET}{BRIGHT_MAGENTA}")
    if rma_value.isdigit() and int(rma_value) in [1, 2]:
        break
    print(f"{Style.BRIGHT}{Fore.RED}Oops, out of range. Please enter a value 1 or 2.")

# Get backtest setting
while True:
    backtest = input(f"{Style.BRIGHT}{Fore.RED}𓆩✧𓆪 Advance Backtest (1.ON 0.OFF): {Style.BRIGHT}{Fore.MAGENTA}").strip()
    if backtest in ['0', '1']:
        backtest = int(backtest)
        break
    print(f"{Fore.RED}Invalid input. Please enter 0 or 1.")

# Get percentage settings
while True:
    percentage = int(input(f"{Style.BRIGHT}{Fore.GREEN}𓆩✧𓆪 Enter the minimum percentage (50-90): {Style.BRIGHT}{Fore.MAGENTA}").strip())
    if 50 <= percentage <= 90:
        break

while True:
    max_percentage = float(input(f"{Style.BRIGHT}{Fore.GREEN}𓆩✧𓆪 Enter the maximum percentage (78-95): {Style.BRIGHT}{Fore.MAGENTA}").strip())
    if 78 <= max_percentage <= 95:
        break

# Get days setting
try:
    while True:
        days = int(input(f"{Style.BRIGHT}{Fore.LIGHTYELLOW_EX}𓆩✧𓆪 Enter the number of Backtest days to analyze (eg. 20): {Style.BRIGHT}{Fore.MAGENTA}").strip())
        if 0 <= days <= 30:
            break
        print(f"{Fore.RED}Invalid input. Please enter a number between 0 and 30.")
except ValueError:
    while True:
        days = input(f"{BOLD}{BRIGHT_BLUE}𓆩✧𓆪Enter number of days (0-20): {RESET}")
        if days.isdigit() and 0 <= int(days) <= 20:
            break
        print(f"{BRIGHT_RED}Invalid number of days. Please enter a value between 3 and 5.{RESET}")
        days = input(f"{BOLD}{BRIGHT_BLUE}𓆩✧𓆪Enter number of days (0-20): {RESET}")

# Get time settings
start_time_input = input(f"{BOLD}{BRIGHT_GREEN}𓆩✧𓆪[+] Start Time (HH:MM): {RESET}")
end_time_input = input(f"{BOLD}{BRIGHT_YELLOW}𓆩✧𓆪[+] End Time (HH:MM): {RESET}")

def validate_time_format(time_str):
    try:
        datetime.datetime.strptime(time_str, '%H:%M')
        return True
    except ValueError:
        return False

while not (validate_time_format(start_time_input) and validate_time_format(end_time_input)):
    print(f"{BRIGHT_RED}Invalid time format! Please use HH:MM format.{RESET}")
    start_time_input = input(f"{BOLD}{BRIGHT_GREEN}𓆩✧𓆪[+] Start Time (HH:MM): {RESET}")
    end_time_input = input(f"{BOLD}{BRIGHT_YELLOW}𓆩✧𓆪[+] End Time (HH:MM): {RESET}")

start_time = datetime.datetime.strptime(start_time_input, '%H:%M')
end_time = datetime.datetime.strptime(end_time_input, '%H:%M')
if start_time >= end_time:
    print(f"{BRIGHT_RED}Start Time must be earlier than End Time! Exiting...{RESET}")
    exit()

# Asset selection
assets = ['AUD/CAD-OTC', 'AUD/CHF-OTC', 'AUD/JPY-OTC', 'AUD/NZD-OTC', 'AUD/USD-OTC', 'BRL/USD-OTC', 'BTC/USD-OTC', 'CAD/JPY-OTC', 'CAD/CHF-OTC', 'CHF/JPY-OTC', 'DJIUSD', 'EUR/JPY-OTC', 'EUR/SGD-OTC', 'EUR/USD-OTC', 'F40EUR', 'FB-OTC', 'FTSGBP', 'GBP/JPY-OTC', 'GBP/USD-OTC', 'INTC-OTC']

print(f"{BOLD}{BRIGHT_CYAN}═══════════════════════════════════════════")
print("Available Assets:")
for i, asset in enumerate(assets, start=1):
    print(f"{i:2}: {asset:<15}", end="\t")
    if i % 4 == 0:
        print()
print(f"\n═══════════════════════════════════════════{RESET}")

# Get pair selection
pair_choice = input(f"{BOLD}{BRIGHT_MAGENTA}Enter the numbers corresponding to the pairs (e.g., 1,2,3): {RESET}")
selected_pairs = []
for num in pair_choice.split(","):
    if num.isdigit() and 1 <= int(num) <= len(assets):
        selected_pairs.append(assets[int(num) - 1])

if not selected_pairs:
    print(f"{BRIGHT_RED}No valid pairs selected. Exiting...{RESET}")
    exit()

print(f"\n{BOLD}{BRIGHT_GREEN}Selected Pairs: {', '.join(selected_pairs)}{RESET}")

# Display header
footer = f"{BOLD}{BRIGHT_CYAN}{'=' * 50}\n{'UTC +6:00 TIMEFRAME:'.center(50)}\n{'=' * 50}{RESET}"
print(footer)

time.sleep(2)

# Generate signals based on time range
signals = []
current_time = start_time
while current_time <= end_time:
    for pair in selected_pairs:
        signal_time = current_time.strftime('%H:%M')
        direction = random.choice(['CALL', 'PUT'])
        signals.append(f"{pair} > {signal_time} > {direction}")
        current_time += timedelta(minutes=random.randint(1, 5))
        if current_time > end_time:
            break

print(f"\n{BOLD}{BRIGHT_GREEN}Generated Signals:{RESET}")
for signal in signals:
    print(f"{BOLD}{BRIGHT_RED}{signal}{RESET}")

# Display footer
footer = f"{BOLD}{BRIGHT_CYAN}{'=' * 50}\n{'PRAGTON X2'.center(50)}\n{'=' * 50}{RESET}"
print(footer)

# Wait for 10 minutes (600 seconds) before exiting
start_time = time.time()
while time.time() - start_time < 600:
    time.sleep(1)
sys.exit()
