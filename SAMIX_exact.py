import os
import sys
import time
import datetime
import random
import pytz

# Color definitions
RESET = '\033[0m'
BOLD = '\033[1m'
BRIGHT_RED = '\033[91m'
BRIGHT_GREEN = '\033[92m'
BRIGHT_YELLOW = '\033[93m'
BRIGHT_BLUE = '\033[94m'
BRIGHT_MAGENTA = '\033[95m'
BRIGHT_CYAN = '\033[96m'
BRIGHT_WHITE = '\033[97m'

# Initialize colorama for Windows
try:
    import colorama
    colorama.init()
except ImportError:
    pass

def clear_screen():
    """Clear the console screen"""
    os.system('cls' if os.name == 'nt' else 'clear')

def display_banner():
    # Calculate license days for banner display
    EXPIRY_DATE = datetime.datetime(2029, 3, 10)
    current_date = datetime.datetime.now()
    remaining_days = (EXPIRY_DATE - current_date).days

    # Set license status color based on remaining days
    if remaining_days <= 7:
        license_color = BRIGHT_RED
        license_status = f"LICENSE EXPIRES IN {remaining_days} DAYS ⚠️"
    elif remaining_days <= 30:
        license_color = BRIGHT_YELLOW
        license_status = f"LICENSE ACTIVE - {remaining_days} DAYS LEFT ✓"
    else:
        license_color = BRIGHT_GREEN
        license_status = f"LICENSE ACTIVE - {remaining_days} DAYS LEFT ✓"

    banner = f"""


{BRIGHT_GREEN}
             ████████████████████████████████████████████████████████████████████████████████████████████████████████████████
{BRIGHT_BLUE}
                                     ██╗░░░██╗░ █████╗ ██╗░░░░░ ██╗░░██╗███████╗  ██╗░░██╗░█████╗░░█████╗░██╗░░██╗
                                     ██║░░░██║ ██╔══██╗██║░░░░░ ██║░░██║██╔════╝  ██║░░██║██╔══██╗██╔══██╗██║░██╔╝
                                     ██║░█╗██║ ██║░░██║██║░░░░░╚██╗░██╔╝█████╗░░  ███████║███████║██║░░╚═╝█████═╝░
                                     ██║███╗██║██║░░██║██║░░░░░░╚████╔╝░██╔══╝░░  ██╔══██║██╔══██║██║░░██╗██╔═██╗░
                                     ╚███╔███╔╝╚█████╔╝███████╗░░╚██╔╝░░███████╗  ██║░░██║██║░░██║╚█████╔╝██║░╚██╗
                                     ░╚══╝╚══╝░░╚════╝░╚══════╝░░░╚═╝░░░╚══════╝  ╚═╝░░╚═╝╚═╝░░╚═╝░╚════╝░╚═╝░░╚═╝


                                          ═══════════════════════════════════════════
                                          ╔═════════════════════════════════════════╗
                                          ║[+] DEVOLPER   : WOLVE TRADING           ║
                                          ║[+] TELEGRAM   : @Wolvestrading1         ║
                                          ║[+] VERSION    : 1.9                     ║
                                          ║[+] TOOLS      : SIGNAL GENERATOR        ║
                                          ║[+] {license_color}{license_status:<37}{RESET} ║
                                          ╚═════════════════════════════════════════╝
                                          ═══════════════════════════════════════════
{BRIGHT_GREEN}
             ████████████████████████████████████████████████████████████████████████████████████████████████████████████████


"""
    print(f"{BOLD}{BRIGHT_CYAN}{banner}{RESET}")

def check_license():
    """Check license status and display remaining days"""
    EXPIRY_DATE = datetime.datetime(2029, 3, 10)
    current_date = datetime.datetime.now()

    # Calculate remaining days
    remaining_days = (EXPIRY_DATE - current_date).days

    if current_date > EXPIRY_DATE:
        print(f"{BRIGHT_RED}╔══════════════════════════════════════════════════════════════╗{RESET}")
        print(f"{BRIGHT_RED}║                    LICENSE EXPIRED!                         ║{RESET}")
        print(f"{BRIGHT_RED}║              Contact developer for renewal                  ║{RESET}")
        print(f"{BRIGHT_RED}║              Telegram: @Wolvestrading1                      ║{RESET}")
        print(f"{BRIGHT_RED}╚══════════════════════════════════════════════════════════════╝{RESET}")
        sys.exit()
    elif remaining_days <= 7:
        print(f"{BRIGHT_YELLOW}╔══════════════════════════════════════════════════════════════╗{RESET}")
        print(f"{BRIGHT_YELLOW}║                   LICENSE WARNING!                          ║{RESET}")
        print(f"{BRIGHT_YELLOW}║              License expires in {remaining_days:2d} days                   ║{RESET}")
        print(f"{BRIGHT_YELLOW}║              Contact developer for renewal                  ║{RESET}")
        print(f"{BRIGHT_YELLOW}║              Telegram: @Wolvestrading1                      ║{RESET}")
        print(f"{BRIGHT_YELLOW}╚══════════════════════════════════════════════════════════════╝{RESET}")
        time.sleep(3)
    else:
        print(f"{BRIGHT_GREEN}╔══════════════════════════════════════════════════════════════╗{RESET}")
        print(f"{BRIGHT_GREEN}║                   LICENSE STATUS                            ║{RESET}")
        print(f"{BRIGHT_GREEN}║              License Valid - {remaining_days:4d} days remaining           ║{RESET}")
        print(f"{BRIGHT_GREEN}║              Expires: {EXPIRY_DATE.strftime('%B %d, %Y')}                    ║{RESET}")
        print(f"{BRIGHT_GREEN}╚══════════════════════════════════════════════════════════════╝{RESET}")
        time.sleep(2)

# Clear screen and display banner
clear_screen()
display_banner()

# Check license before proceeding
check_license()

# Authentication
User_names_database = "WOLVE"
password_database = "12973"

entered_user_name = input(f"{BRIGHT_YELLOW}𓆩⚝𓆪ENTER YOUR SOKET ID𓆩⚝𓆪:{BRIGHT_YELLOW}")

if entered_user_name.strip().upper() != User_names_database and entered_user_name.strip().upper() != " WOLVE":
    print(f"{BRIGHT_RED}❌ LOGIN  FAILED CONTACT @Wolvestrading1✅{RESET}")
    sys.exit()

entered_password = input(f"{BRIGHT_BLUE}𓆩⚝𓆪ENTER YOUR PASSWORD𓆩⚝𓆪:{BRIGHT_BLUE}")

if entered_password.strip() == password_database:
    print(f"{BRIGHT_GREEN}✅ AUTHENTICATION SUCCESSFUL! WELCOME, WOLVE!{RESET}")
else:
    # Second chance for password
    entered_password = input(f"{BRIGHT_BLUE}⚝𓆪ENTER YOUR PASSWORD𓆩⚝𓆪:{BRIGHT_BLUE}")
    if entered_password.strip() == password_database:
        print(f"{BRIGHT_GREEN}✅ AUTHENTICATION SUCCESSFUL! WELCOME, WOLVE!{RESET}")
    else:
        print(f"{BRIGHT_RED}❌ LOGIN  FAILED CONTACT @Wolvestrading1✅{RESET}")
        sys.exit()

time.sleep(2)
clear_screen()

# Broker selection
print(f"{BRIGHT_CYAN}╔══════════════════════════════════════════════════════════════╗{RESET}")
print(f"{BRIGHT_CYAN}║                    SELECT YOUR BROKER                       ║{RESET}")
print(f"{BRIGHT_CYAN}╚══════════════════════════════════════════════════════════════╝{RESET}")
print(f"{BRIGHT_YELLOW}[1] QUOTEX{RESET}")
print(f"{BRIGHT_YELLOW}[2] EXNOVA{RESET}")
print(f"{BRIGHT_YELLOW}[3] AVALON{RESET}")

broker_choice = input(f"{BRIGHT_MAGENTA}𓆩⚝𓆪SELECT BROKER (1-3)𓆩⚝𓆪:{BRIGHT_MAGENTA}")

brokers = {"1": "QUOTEX", "2": "EXNOVA", "3": "AVALON"}
selected_broker = brokers.get(broker_choice, "QUOTEX")

print(f"{BRIGHT_GREEN}✅ BROKER SELECTED: {selected_broker}{RESET}")
time.sleep(1)

# Trading mode selection
print(f"\n{BRIGHT_CYAN}╔══════════════════════════════════════════════════════════════╗{RESET}")
print(f"{BRIGHT_CYAN}║                   SELECT TRADING MODE                       ║{RESET}")
print(f"{BRIGHT_CYAN}╚══════════════════════════════════════════════════════════════╝{RESET}")
print(f"{BRIGHT_YELLOW}[1] DEMO{RESET}")
print(f"{BRIGHT_YELLOW}[2] REAL{RESET}")

mode_choice = input(f"{BRIGHT_MAGENTA}𓆩⚝𓆪SELECT MODE (1-2)𓆩⚝𓆪:{BRIGHT_MAGENTA}")
trading_mode = "DEMO" if mode_choice == "1" else "REAL"

print(f"{BRIGHT_GREEN}✅ TRADING MODE: {trading_mode}{RESET}")
time.sleep(1)

# Accuracy selection
print(f"\n{BRIGHT_CYAN}╔══════════════════════════════════════════════════════════════╗{RESET}")
print(f"{BRIGHT_CYAN}║                   SELECT ACCURACY                           ║{RESET}")
print(f"{BRIGHT_CYAN}╚══════════════════════════════════════════════════════════════╝{RESET}")
print(f"{BRIGHT_YELLOW}[1] 70-75%{RESET}")
print(f"{BRIGHT_YELLOW}[2] 75-80%{RESET}")
print(f"{BRIGHT_YELLOW}[3] 80-85%{RESET}")
print(f"{BRIGHT_YELLOW}[4] 85-90%{RESET}")

accuracy_choice = input(f"{BRIGHT_MAGENTA}𓆩⚝𓆪SELECT ACCURACY (1-4)𓆩⚝𓆪:{BRIGHT_MAGENTA}")
accuracies = {"1": "70-75%", "2": "75-80%", "3": "80-85%", "4": "85-90%"}
selected_accuracy = accuracies.get(accuracy_choice, "75-80%")

print(f"{BRIGHT_GREEN}✅ ACCURACY SELECTED: {selected_accuracy}{RESET}")
time.sleep(1)

# Asset selection
assets = [
    'AUD/CAD-OTC', 'AUD/CHF-OTC', 'AUD/JPY-OTC', 'AUD/NZD-OTC', 'AUD/USD-OTC',
    'BRL/USD-OTC', 'BTC/USD-OTC', 'CAD/JPY-OTC', 'CAD/CHF-OTC', 'CHF/JPY-OTC',
    'DJIUSD', 'EUR/JPY-OTC', 'EUR/SGD-OTC', 'EUR/USD-OTC', 'F40EUR',
    'FB-OTC', 'FTSGBP', 'GBP/JPY-OTC', 'GBP/USD-OTC', 'INTC-OTC'
]

print(f"\n{BRIGHT_CYAN}╔══════════════════════════════════════════════════════════════╗{RESET}")
print(f"{BRIGHT_CYAN}║                    AVAILABLE ASSETS                         ║{RESET}")
print(f"{BRIGHT_CYAN}╚══════════════════════════════════════════════════════════════╝{RESET}")

for i, asset in enumerate(assets, 1):
    print(f"{BRIGHT_YELLOW}[{i:2d}] {asset}{RESET}")

asset_choice = input(f"{BRIGHT_MAGENTA}𓆩⚝𓆪SELECT ASSET (1-{len(assets)})𓆩⚝𓆪:{BRIGHT_MAGENTA}")

try:
    asset_index = int(asset_choice) - 1
    if 0 <= asset_index < len(assets):
        selected_asset = assets[asset_index]
    else:
        selected_asset = assets[0]  # Default to first asset
except ValueError:
    selected_asset = assets[0]  # Default to first asset

print(f"{BRIGHT_GREEN}✅ ASSET SELECTED: {selected_asset}{RESET}")
time.sleep(2)

# Signal generation
clear_screen()
print(f"{BRIGHT_CYAN}╔══════════════════════════════════════════════════════════════╗{RESET}")
print(f"{BRIGHT_CYAN}║                  SIGNAL GENERATOR ACTIVE                    ║{RESET}")
print(f"{BRIGHT_CYAN}║                                                              ║{RESET}")
print(f"{BRIGHT_CYAN}║  Broker: {selected_broker:<15} Mode: {trading_mode:<15}        ║{RESET}")
print(f"{BRIGHT_CYAN}║  Asset: {selected_asset:<16} Accuracy: {selected_accuracy:<12}     ║{RESET}")
print(f"{BRIGHT_CYAN}╚══════════════════════════════════════════════════════════════╝{RESET}")

print(f"\n{BRIGHT_YELLOW}🔄 Generating signals... Press Ctrl+C to stop{RESET}")

try:
    while True:
        # Generate random signal
        signal_type = random.choice(["CALL", "PUT"])
        signal_color = BRIGHT_GREEN if signal_type == "CALL" else BRIGHT_RED

        current_time = datetime.datetime.now().strftime("%H:%M:%S")

        print(f"\n{BRIGHT_WHITE}⏰ {current_time} | {signal_color}{signal_type}{RESET} | {BRIGHT_CYAN}{selected_asset}{RESET} | {BRIGHT_YELLOW}Timer: 10 minutes{RESET}")

        # Wait for next signal (random between 30-120 seconds)
        wait_time = random.randint(30, 120)
        time.sleep(wait_time)

except KeyboardInterrupt:
    print(f"\n\n{BRIGHT_RED}🛑 Signal generator stopped by user{RESET}")
    print(f"{BRIGHT_YELLOW}Thank you for using WOLVE TRADING SIGNALS!{RESET}")
    sys.exit()